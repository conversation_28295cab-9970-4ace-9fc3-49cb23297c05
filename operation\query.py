"""
查询知识库记录
用户输入query
"""

from fastapi import APIRouter,HTTPException
from pydantic import BaseModel
from typing import List
from milvus import MilvusVanna

milvus_client = MilvusVanna()


router = APIRouter(prefix="/query", tags=["用户查询"])

class Query(BaseModel):
    # 用户问题
    query:str
    # 知识库的id
    rag_id:List[str]

class Response(BaseModel):
    # 回答
    answer:str
    # 文本块来源
    sources: List[dict]


@router.post("/", response_model=Response)
async def query_endpoint(query: Query):
    try:
        # 1. milvus查询获取的内容
        content_list = milvus_client.query(query.query, query.rag_id, limit=5)
        
        if not content_list:
            raise HTTPException(status_code=404, detail="No relevant content found in Milvus")

        # 2. 整合内容
        combined_content = "\n\n".join([item["content"] for item in content_list])
        
        # 3. 构建提示词
        prompt = [
            {"role": "system", "content": "你是一位知识库问答专家"},
            {
                "role": "user",
                "content": f"请基于以下内容：\n{combined_content}\n请回答以下问题：{query.query}；尽量全面概括，不要回答除问题以外的内容"
            }
        ]

        # 4. Call LLM to generate answer
        response = milvus_client.client.chat.completions.create(
            model=milvus_client.llm_model_name,
            messages=prompt,
            max_tokens=500,  # Adjust as needed
            temperature=0.7  # Adjust as needed
        )
        answer = response.choices[0].message.content.strip()

        # 5. Format sources for response
        sources = [
            {
                "id": item["id"],
                "rag_id": item["rag_id"],
                "source": item["source"],
                "chunk_id": item["chunk_id"],
                "score": item["score"],
                "content": item["content"][:200]  # Truncate for brevity
            }
            for item in content_list
        ]

        # 6. Return structured response
        return Response(
            answer=answer,
            sources=sources
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Query failed: {str(e)}")