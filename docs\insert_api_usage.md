# 插入数据 API 使用说明

## 概述

知识库系统支持多种方式的数据插入操作：

- **文本内容插入**: 直接提供文本内容进行插入
- **文件上传插入**: 上传文件并自动提取内容进行插入
- **批量插入**: 批量插入多个文本内容

支持的更新模式：
- **增量更新** (`incremental`): 检查内容是否已存在，避免重复插入
- **全量更新** (`full`): 先删除文档的所有记录，再插入新内容

支持的文本分块策略：
- **固定分块** (`fixed`): 按固定大小分块
- **语义分块** (`semantic`): 按语义相似度分块
- **混合分块** (`hybrid`): 先语义分块，再固定大小分块（推荐）

## API 端点

### 1. 插入文本内容

**POST** `/insert/text`

直接提供文本内容进行插入。

#### 请求体格式

```json
{
  "rag_id": "rag_001",                    // 必需：知识库ID
  "doc_id": "doc_001",                    // 可选：文档ID，不提供则自动生成
  "source": "manual_input",               // 可选：来源描述
  "content": "这是要插入的文本内容...",      // 必需：文本内容
  "split_strategy": "hybrid",             // 可选：分块策略，默认hybrid
  "update_mode": "incremental"            // 可选：更新模式，默认incremental
}
```

#### 响应格式

```json
{
  "success": true,
  "message": "成功插入 5 个文本块",
  "doc_id": "doc_001",
  "chunks_count": 5,
  "details": {
    "split_strategy": "hybrid",
    "update_mode": "incremental",
    "source": "manual_input"
  }
}
```

### 2. 文件上传插入

**POST** `/insert/file`

上传文件并自动提取内容进行插入。

#### 请求参数

- `file`: 上传的文件（支持 txt, pdf, doc, docx, xls, xlsx, csv, ppt, pptx）
- `rag_id`: 知识库ID（必需）
- `doc_id`: 文档ID（可选）
- `source`: 来源描述（可选）
- `split_strategy`: 分块策略（可选，默认hybrid）
- `update_mode`: 更新模式（可选，默认incremental）

#### 响应格式

```json
{
  "success": true,
  "message": "成功插入 8 个文本块",
  "doc_id": "auto_generated_uuid",
  "chunks_count": 8,
  "details": {
    "split_strategy": "hybrid",
    "update_mode": "incremental",
    "source": "uploaded_document.pdf"
  }
}
```

### 3. 批量插入

**POST** `/insert/batch`

批量插入多个文本内容。

#### 请求体格式

```json
[
  {
    "rag_id": "rag_001",
    "content": "第一个文本内容...",
    "source": "source_1",
    "split_strategy": "hybrid",
    "update_mode": "incremental"
  },
  {
    "rag_id": "rag_002",
    "content": "第二个文本内容...",
    "source": "source_2",
    "split_strategy": "semantic",
    "update_mode": "full"
  }
]
```

#### 响应格式

```json
{
  "success": true,
  "message": "批量插入完成：成功 2 项，失败 0 项",
  "result": {
    "total": 2,
    "success": 2,
    "failed": 0,
    "details": [
      {
        "index": 0,
        "doc_id": "uuid_1",
        "success": true,
        "message": "成功插入 3 个文本块",
        "chunks_count": 3
      },
      {
        "index": 1,
        "doc_id": "uuid_2",
        "success": true,
        "message": "成功插入 5 个文本块",
        "chunks_count": 5
      }
    ]
  }
}
```

### 4. 健康检查

**GET** `/insert/health`

检查插入服务的健康状态。

#### 响应格式

```json
{
  "status": "healthy",
  "service": "insert",
  "timestamp": "2025-06-20T10:30:00"
}
```

## 使用示例

### 示例 1: 插入文本内容

```bash
curl -X POST "http://localhost:3008/insert/text" \
  -H "Content-Type: application/json" \
  -d '{
    "rag_id": "knowledge_base_001",
    "content": "这是一段关于人工智能的介绍文本。人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
    "source": "AI_introduction",
    "split_strategy": "hybrid",
    "update_mode": "incremental"
  }'
```

### 示例 2: 上传PDF文件

```bash
curl -X POST "http://localhost:3008/insert/file" \
  -F "file=@document.pdf" \
  -F "rag_id=knowledge_base_001" \
  -F "source=technical_manual" \
  -F "split_strategy=hybrid" \
  -F "update_mode=full"
```

### 示例 3: 批量插入

```bash
curl -X POST "http://localhost:3008/insert/batch" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "rag_id": "kb_001",
      "content": "第一段文本内容...",
      "source": "document_1"
    },
    {
      "rag_id": "kb_001", 
      "content": "第二段文本内容...",
      "source": "document_2"
    }
  ]'
```

### 示例 4: Python 客户端调用

```python
import requests

# 插入文本内容
def insert_text(rag_id, content, source=None):
    url = "http://localhost:3008/insert/text"
    data = {
        "rag_id": rag_id,
        "content": content,
        "source": source,
        "split_strategy": "hybrid",
        "update_mode": "incremental"
    }
    
    response = requests.post(url, json=data)
    return response.json()

# 上传文件
def upload_file(file_path, rag_id, source=None):
    url = "http://localhost:3008/insert/file"
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {
            'rag_id': rag_id,
            'source': source or file_path,
            'split_strategy': 'hybrid',
            'update_mode': 'incremental'
        }
        
        response = requests.post(url, files=files, data=data)
        return response.json()

# 使用示例
result1 = insert_text("kb_001", "这是测试文本内容", "test_source")
print(result1)

result2 = upload_file("document.pdf", "kb_001", "pdf_document")
print(result2)
```

## 错误处理

### 常见错误码

- **400 Bad Request**: 请求参数错误
  - 缺少必需参数
  - 文件类型不支持
  - 文本内容为空

- **500 Internal Server Error**: 服务器内部错误
  - 文件内容提取失败
  - 数据库连接失败
  - 文本分块失败

### 错误响应格式

```json
{
  "success": false,
  "message": "插入失败: 文本内容不能为空",
  "doc_id": "",
  "chunks_count": 0,
  "details": {
    "error": "文本内容不能为空"
  }
}
```

## 注意事项

1. **文件大小限制**: 建议单个文件不超过 50MB
2. **文本长度**: 单个文本块建议不超过 65535 字符
3. **并发限制**: 建议同时上传的文件数量不超过 10 个
4. **支持的文件类型**: txt, pdf, doc, docx, xls, xlsx, csv, ppt, pptx
5. **编码格式**: 文本文件建议使用 UTF-8 编码

## 依赖安装

如需支持所有文件类型，请安装以下依赖：

```bash
pip install python-docx PyPDF2 pandas openpyxl python-pptx
```

如果某些依赖未安装，系统会在处理对应文件类型时给出明确的错误提示。
