##########mysql数据库账号密码
config = {
    'pool_name': 'oper_stable',
    'host': '*************',
    'port': 3306,
    'user': 'root',
    'password': 'ffkj1314',
    'database': 'chatdb',
}



connection = "mysql+pymysql://%s:%s@%s:%s/%s?charset=utf8"%(config["user"],config["password"],config["host"],config["port"],config["database"])
PORT = 3008


##########milvus的库
MILVUS_HOST = '*************'
MILVUS_PORT ="19530" 
vector_dim = 384
index_params = {
    "metric_type": "IP",
    "index_type":"IVF_SQ8",
    "params":{"nlist":16384}
    }
search_params = {
    "metric_type": "IP",
    "params": {"nprobe": 16}
}
collection_name = ""

##########QWEN配置-深i企
# LLM_API_KEY = 'sk-e26d208fa19f427f9b98ed849e9e7998'
# LLM_BASE_URL = 'https://dashscope.aliyuncs.com/compatible-mode/v1'
# LLM_MODEL_NAME = "qwen-plus"
# EMBED_MODEL_NAME = "text-embedding-v3"

########## 硅基流动
LLM_API_KEY = 'sk-cyckyrzrjkibrfzreegbeynoafczsdneuedfvphnkszjmygn'
LLM_BASE_URL = 'https://api.siliconflow.cn/v1'
LLM_MODEL_NAME = "Qwen/Qwen2.5-72B-Instruct"
# LLM_BASE_URL = 'http://*************:8008/api'
EMBEDDING_MODEL_PATH = r'E:\work\2025_fufeng\nl2\embedding_model\sentence-transformers\msmarco-MiniLM-L-6-v3'