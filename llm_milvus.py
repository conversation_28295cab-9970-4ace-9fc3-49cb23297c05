"""
配置LLM和milvus client等统一调用函数
"""

from openai import OpenAI
import sys
import os 
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import LLM_API_KEY,LLM_BASE_URL,LLM_MODEL_NAME,MILVUS_HOST,MILVUS_PORT
from pymilvus import connections, MilvusClient


# 初始化 OpenAI 客户端 ———— 硅基流动
client = OpenAI(
    base_url = LLM_BASE_URL,
    api_key = LLM_API_KEY
)


# 先建立老API连接，供 utility/ORM 层使用
connections.connect(
    alias="default",
    host=MILVUS_HOST,
    port=MILVUS_PORT
)

# 初始化 MilvusClient 实例（新API，推荐用于 schema/collection 操作）
MILVUS_URI = f"http://{MILVUS_HOST}:{MILVUS_PORT}"
MilvusClient = MilvusClient(uri=MILVUS_URI)


# 构造提交的提示词函数，输入对应构造好的prompt即可使用
def submit_prompt(prompt, **kwargs) -> str:
    """使用 OpenAI SDK 提交 prompt"""
    try:
        response = client.chat.completions.create(
            model = LLM_MODEL_NAME,
            messages=prompt,
            temperature=kwargs.get("temperature", 0.7),
            max_tokens=kwargs.get("max_tokens", 4096),
            # 非流式输出
            stream=kwargs.get("stream", False)
        )

        if kwargs.get("stream", False):
            full_response = ""
            for chunk in response:
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_response += content
                    print(content, end="", flush=True)
            return full_response
        else:
            return response.choices[0].message.content

    except Exception as e:
        raise RuntimeError(f"LLM request failed: {e}")