"""
创建时间：2025.06.17
注释：通用文本切分——固定chunk,语义,混合（语义+固定）
"""
from typing import List, Optional
import re
from langchain.text_splitter import RecursiveCharacterTextSplitter
import numpy as np
from dataclasses import dataclass
import logging
from milvus import milvus_client


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SplitConfig:
    """文本切分配置类"""
    chunk_size: int = 1024  # 固定chunk大小
    chunk_overlap: int = 256  # chunk重叠大小
    min_chunk_size: int = 100  # 最小chunk大小
    semantic_threshold: float = 0.7  # 语义相似度阈值

class TextSplitter:
    """文本切分器类，支持固定chunk、语义和混合切分策略"""
    
    def __init__(self, config: Optional[SplitConfig] = None):
        """
        初始化文本切分器
        
        Args:
            config: 切分配置，如果为None则使用默认配置
        """
        self.config = config or SplitConfig()
        self.recursive_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap
        )
        try:
            self.semantic_model = milvus_client.embedding_model
        except Exception as e:
            logger.error(f"语义模型加载失败: {str(e)}")
            self.semantic_model = None

    def split_by_fixed_chunk(self, text: str) -> List[str]:
        """
        使用固定大小进行文本切分
        
        Args:
            text: 待切分的文本
            
        Returns:
            切分后的文本块列表
        """
        try:
            chunks = self.recursive_splitter.split_text(text)
            return [chunk for chunk in chunks if len(chunk) >= self.config.min_chunk_size]
        except Exception as e:
            logger.error(f"固定chunk切分失败: {str(e)}")
            return [text]

    def split_by_semantic(self, text: str) -> List[str]:
        """
        使用语义相似度进行文本切分
        
        Args:
            text: 待切分的文本
            
        Returns:
            切分后的文本块列表
        """
        if not self.semantic_model:
            logger.warning("语义模型未加载，回退到固定chunk切分")
            return self.split_by_fixed_chunk(text)

        try:
            # 首先按句子分割
            sentences = re.split(r'[。！？.!?]', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if len(sentences) <= 1:
                return [text]

            # 计算句子嵌入
            embeddings = self.semantic_model.encode(sentences)
            
            # 基于语义相似度合并句子
            chunks = []
            current_chunk = []
            current_embedding = None
            
            for i, (sentence, embedding) in enumerate(zip(sentences, embeddings)):
                if not current_chunk:
                    current_chunk.append(sentence)
                    current_embedding = embedding
                else:
                    # 计算当前句子与当前chunk的语义相似度
                    similarity = np.dot(embedding, current_embedding) / (
                        np.linalg.norm(embedding) * np.linalg.norm(current_embedding)
                    )
                    
                    if similarity >= self.config.semantic_threshold:
                        current_chunk.append(sentence)
                        # 更新当前chunk的嵌入（简单平均）
                        current_embedding = (current_embedding * len(current_chunk) + embedding) / (len(current_chunk) + 1)
                    else:
                        # 保存当前chunk并开始新的chunk
                        if current_chunk:
                            chunks.append(''.join(current_chunk))
                        current_chunk = [sentence]
                        current_embedding = embedding
            
            # 添加最后一个chunk
            if current_chunk:
                chunks.append(''.join(current_chunk))
            
            return chunks
        except Exception as e:
            logger.error(f"语义切分失败: {str(e)}")
            return self.split_by_fixed_chunk(text)

    def split_by_hybrid(self, text: str) -> List[str]:
        """
        使用混合策略进行文本切分（先语义切分，再固定chunk切分）
        
        Args:
            text: 待切分的文本
            
        Returns:
            切分后的文本块列表
        """
        try:
            # 首先进行语义切分
            semantic_chunks = self.split_by_semantic(text)
            
            # 对每个语义chunk进行固定大小切分
            final_chunks = []
            for chunk in semantic_chunks:
                if len(chunk) <= self.config.chunk_size:
                    final_chunks.append(chunk)
                else:
                    fixed_chunks = self.split_by_fixed_chunk(chunk)
                    final_chunks.extend(fixed_chunks)
            
            return final_chunks
        except Exception as e:
            logger.error(f"混合切分失败: {str(e)}")
            return self.split_by_fixed_chunk(text)

    def split(self, text: str, strategy: str = "hybrid") -> List[str]:
        """
        根据指定策略进行文本切分
        
        Args:
            text: 待切分的文本
            strategy: 切分策略，可选值：'fixed', 'semantic', 'hybrid'
            
        Returns:
            切分后的文本块列表
        """
        if not text:
            return []
            
        strategy = strategy.lower()
        #  使用固定大小进行文本切分
        if strategy == "fixed":
            return self.split_by_fixed_chunk(text)
        # 使用语义相似度进行文本切分
        elif strategy == "semantic":
            return self.split_by_semantic(text)
        # 使用混合策略进行文本切分
        elif strategy == "hybrid":
            return self.split_by_hybrid(text)
        else:
            logger.warning(f"未知的切分策略: {strategy}，使用默认的混合策略")
            return self.split_by_hybrid(text)

    def summarize_by_percentage(self, text: str, percentage: float = 0.3) -> str:
        """
        将长文本进行摘要，提取文本内容的指定百分比（按字符长度）
        
        Args:
            text: 待摘要的长文本
            percentage: 摘要的百分比（例如，0.3 表示30%）
            
        Returns:
            摘要后的文本
        """
        if not self.semantic_model:
            logger.warning("语义模型未加载，无法进行摘要。返回原文本。")
            return text

        if not (0 < percentage <= 1):
            logger.error("百分比必须在 0 到 1 之间。返回原文本。")
            return text

        try:
            sentences = re.split(r'[。！？.!?\n]', text) # 考虑换行符作为句子分隔符
            sentences = [s.strip() for s in sentences if s.strip()]

            if not sentences:
                return ""
            if len(sentences) == 1:
                return sentences[0] # 如果只有一个句子，直接返回

            # 计算整个文本的嵌入
            text_embedding = self.semantic_model.encode([text], normalize_embeddings=True)[0]

            # 计算每个句子的嵌入和与全文的相似度
            sentence_embeddings = self.semantic_model.encode(sentences, normalize_embeddings=True)
            
            # 计算余弦相似度并存储 (相似度, 原始句子索引)
            sentence_scores = []
            for i, sent_emb in enumerate(sentence_embeddings):
                similarity = np.dot(sent_emb, text_embedding) / (
                    np.linalg.norm(sent_emb) * np.linalg.norm(text_embedding)
                )
                sentence_scores.append((similarity, i))

            # 按相似度降序排序
            sentence_scores.sort(key=lambda x: x[0], reverse=True)

            target_length = len(text) * percentage
            current_summary_length = 0
            summary_sentences_indices = set() # 用set确保句子不重复

            # 逐步添加句子直到达到目标长度
            for score, original_index in sentence_scores:
                if current_summary_length >= target_length:
                    break
                if original_index not in summary_sentences_indices:
                    summary_sentences_indices.add(original_index)
                    current_summary_length += len(sentences[original_index])
            
            # 按原始顺序重构摘要
            sorted_summary_sentences = sorted([sentences[idx] for idx in list(summary_sentences_indices)], key=lambda x: sentences.index(x))
            
            return "".join(sorted_summary_sentences)

        except Exception as e:
            logger.error(f"文本摘要失败: {str(e)}")
            return text