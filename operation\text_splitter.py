"""
创建时间：2025.06.17
注释：通用文本切分——固定chunk,语义,混合（语义+固定）
"""
from typing import List, Optional
import re
from langchain.text_splitter import RecursiveCharacterTextSplitter
import numpy as np
from dataclasses import dataclass
import logging
try:
    # 尝试相对导入（当作为模块运行时）
    from .milvus import milvus_client
except ImportError:
    # 尝试绝对导入（当直接运行时）
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from operation.milvus import milvus_client


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SplitConfig:
    """文本切分配置类"""
    chunk_size: int = 1024  # 固定chunk大小
    chunk_overlap: int = 256  # chunk重叠大小
    min_chunk_size: int = 100  # 最小chunk大小
    semantic_threshold: float = 0.7  # 语义相似度阈值

class TextSplitter:
    """文本切分器类，支持固定chunk、语义和混合切分策略"""
    
    def __init__(self, config: Optional[SplitConfig] = None):
        """
        初始化文本切分器
        
        Args:
            config: 切分配置，如果为None则使用默认配置
        """
        self.config = config or SplitConfig()
        self.recursive_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap
        )
        try:
            self.semantic_model = milvus_client.embedding_model
        except Exception as e:
            logger.error(f"语义模型加载失败: {str(e)}")
            self.semantic_model = None

    def split_by_fixed_chunk(self, text: str) -> List[str]:
        """
        使用固定大小进行文本切分
        
        Args:
            text: 待切分的文本
            
        Returns:
            切分后的文本块列表
        """
        try:
            chunks = self.recursive_splitter.split_text(text)
            return [chunk for chunk in chunks if len(chunk) >= self.config.min_chunk_size]
        except Exception as e:
            logger.error(f"固定chunk切分失败: {str(e)}")
            return [text]

    def split_by_semantic(self, text: str) -> List[str]:
        """
        使用语义相似度进行文本切分
        
        Args:
            text: 待切分的文本
            
        Returns:
            切分后的文本块列表
        """
        if not self.semantic_model:
            logger.warning("语义模型未加载，回退到固定chunk切分")
            return self.split_by_fixed_chunk(text)

        try:
            # 首先按句子分割
            sentences = re.split(r'[。！？.!?]', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if len(sentences) <= 1:
                return [text]

            # 计算句子嵌入
            embeddings = self.semantic_model.encode(sentences)
            
            # 基于语义相似度合并句子
            chunks = []
            current_chunk = []
            current_embedding = None
            
            for i, (sentence, embedding) in enumerate(zip(sentences, embeddings)):
                if not current_chunk:
                    current_chunk.append(sentence)
                    current_embedding = embedding
                else:
                    # 计算当前句子与当前chunk的语义相似度
                    similarity = np.dot(embedding, current_embedding) / (
                        np.linalg.norm(embedding) * np.linalg.norm(current_embedding)
                    )
                    
                    if similarity >= self.config.semantic_threshold:
                        current_chunk.append(sentence)
                        # 更新当前chunk的嵌入（简单平均）
                        current_embedding = (current_embedding * len(current_chunk) + embedding) / (len(current_chunk) + 1)
                    else:
                        # 保存当前chunk并开始新的chunk
                        if current_chunk:
                            chunks.append(''.join(current_chunk))
                        current_chunk = [sentence]
                        current_embedding = embedding
            
            # 添加最后一个chunk
            if current_chunk:
                chunks.append(''.join(current_chunk))
            
            return chunks
        except Exception as e:
            logger.error(f"语义切分失败: {str(e)}")
            return self.split_by_fixed_chunk(text)

    def split_by_hybrid(self, text: str) -> List[str]:
        """
        使用混合策略进行文本切分（先语义切分，再固定chunk切分）
        
        Args:
            text: 待切分的文本
            
        Returns:
            切分后的文本块列表
        """
        try:
            # 首先进行语义切分
            semantic_chunks = self.split_by_semantic(text)
            
            # 对每个语义chunk进行固定大小切分
            final_chunks = []
            for chunk in semantic_chunks:
                if len(chunk) <= self.config.chunk_size:
                    final_chunks.append(chunk)
                else:
                    fixed_chunks = self.split_by_fixed_chunk(chunk)
                    final_chunks.extend(fixed_chunks)
            
            return final_chunks
        except Exception as e:
            logger.error(f"混合切分失败: {str(e)}")
            return self.split_by_fixed_chunk(text)

    def split(self, text: str, strategy: str = "hybrid") -> List[str]:
        """
        根据指定策略进行文本切分
        
        Args:
            text: 待切分的文本
            strategy: 切分策略，可选值：'fixed', 'semantic', 'hybrid'
            
        Returns:
            切分后的文本块列表
        """
        if not text:
            return []
            
        strategy = strategy.lower()
        #  使用固定大小进行文本切分
        if strategy == "fixed":
            return self.split_by_fixed_chunk(text)
        # 使用语义相似度进行文本切分
        elif strategy == "semantic":
            return self.split_by_semantic(text)
        # 使用混合策略进行文本切分
        elif strategy == "hybrid":
            return self.split_by_hybrid(text)
        else:
            logger.warning(f"未知的切分策略: {strategy}，使用默认的混合策略")
            return self.split_by_hybrid(text)
