"""
插入API测试脚本
测试文本插入、文件上传、批量插入等功能
"""
import requests
import json
import tempfile
import os
from datetime import datetime

# 配置
BASE_URL = "http://localhost:3008"
INSERT_URL = f"{BASE_URL}/insert"

def test_health_check():
    """测试健康检查"""
    print("=== 测试健康检查 ===")
    try:
        response = requests.get(f"{INSERT_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_insert_text():
    """测试文本内容插入"""
    print("\n=== 测试文本内容插入 ===")
    
    test_data = {
        "rag_id": "test_kb_001",
        "content": """
        人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，
        致力于创建能够执行通常需要人类智能的任务的系统。
        
        AI的主要应用领域包括：
        1. 机器学习和深度学习
        2. 自然语言处理
        3. 计算机视觉
        4. 机器人技术
        5. 专家系统
        
        随着技术的发展，AI正在改变我们的生活和工作方式。
        """,
        "source": "AI_introduction_test",
        "split_strategy": "hybrid",
        "update_mode": "incremental"
    }
    
    try:
        response = requests.post(f"{INSERT_URL}/text", json=test_data)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return response.status_code == 200 and result.get("success", False)
    except Exception as e:
        print(f"文本插入测试失败: {e}")
        return False

def test_insert_text_full_update():
    """测试全量更新模式"""
    print("\n=== 测试全量更新模式 ===")
    
    test_data = {
        "rag_id": "test_kb_002",
        "doc_id": "test_doc_001",
        "content": "这是第一次插入的内容。",
        "source": "full_update_test_1",
        "update_mode": "full"
    }
    
    try:
        # 第一次插入
        response1 = requests.post(f"{INSERT_URL}/text", json=test_data)
        print(f"第一次插入 - 状态码: {response1.status_code}")
        result1 = response1.json()
        print(f"第一次插入 - 响应: {json.dumps(result1, ensure_ascii=False, indent=2)}")
        
        # 第二次插入（全量更新）
        test_data["content"] = "这是第二次插入的内容，应该替换第一次的内容。"
        test_data["source"] = "full_update_test_2"
        
        response2 = requests.post(f"{INSERT_URL}/text", json=test_data)
        print(f"第二次插入 - 状态码: {response2.status_code}")
        result2 = response2.json()
        print(f"第二次插入 - 响应: {json.dumps(result2, ensure_ascii=False, indent=2)}")
        
        return (response1.status_code == 200 and result1.get("success", False) and
                response2.status_code == 200 and result2.get("success", False))
    except Exception as e:
        print(f"全量更新测试失败: {e}")
        return False

def test_batch_insert():
    """测试批量插入"""
    print("\n=== 测试批量插入 ===")
    
    batch_data = [
        {
            "rag_id": "test_kb_batch",
            "content": "这是批量插入的第一个文档内容。包含了关于机器学习的基础知识。",
            "source": "batch_doc_1",
            "split_strategy": "hybrid"
        },
        {
            "rag_id": "test_kb_batch",
            "content": "这是批量插入的第二个文档内容。讨论了深度学习的应用场景。",
            "source": "batch_doc_2",
            "split_strategy": "semantic"
        },
        {
            "rag_id": "test_kb_batch",
            "content": "这是批量插入的第三个文档内容。介绍了自然语言处理技术。",
            "source": "batch_doc_3",
            "split_strategy": "fixed"
        }
    ]
    
    try:
        response = requests.post(f"{INSERT_URL}/batch", json=batch_data)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        return response.status_code == 200 and result.get("success", False)
    except Exception as e:
        print(f"批量插入测试失败: {e}")
        return False

def test_file_upload():
    """测试文件上传"""
    print("\n=== 测试文件上传 ===")

    # 创建临时文本文件
    test_content = """
    深度学习技术指南

    深度学习是机器学习的一个子领域，使用多层神经网络来学习数据的表示。

    主要特点：
    - 自动特征提取
    - 端到端学习
    - 强大的表示能力

    应用领域：
    1. 图像识别
    2. 语音识别
    3. 自然语言处理
    4. 推荐系统

    深度学习已经在许多领域取得了突破性进展。
    """
    
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_file_path = f.name
        
        # 上传文件
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test_document.txt', f, 'text/plain')}
            data = {
                'rag_id': 'test_kb_upload',
                'source': 'uploaded_test_document',
                'split_strategy': 'hybrid',
                'update_mode': 'incremental'
            }
            
            response = requests.post(f"{INSERT_URL}/file", files=files, data=data)
            print(f"状态码: {response.status_code}")
            result = response.json()
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 清理临时文件
        os.unlink(temp_file_path)
        
        return response.status_code == 200 and result.get("success", False)
    except Exception as e:
        print(f"文件上传测试失败: {e}")
        return False

def test_ppt_support():
    """测试PPT文件支持"""
    print("\n=== 测试PPT文件支持 ===")

    try:
        # 检查是否安装了python-pptx
        try:
            from pptx import Presentation
            pptx_available = True
        except ImportError:
            pptx_available = False
            print("⚠️  python-pptx 未安装，跳过PPT测试")
            return True  # 跳过测试但不算失败

        if pptx_available:
            # 创建一个简单的PPT文件用于测试
            prs = Presentation()

            # 添加标题幻灯片
            title_slide_layout = prs.slide_layouts[0]
            slide = prs.slides.add_slide(title_slide_layout)
            title = slide.shapes.title
            subtitle = slide.placeholders[1]
            title.text = "深能集团技术介绍"
            subtitle.text = "清洁能源技术发展报告"

            # 添加内容幻灯片
            bullet_slide_layout = prs.slide_layouts[1]
            slide = prs.slides.add_slide(bullet_slide_layout)
            shapes = slide.shapes
            title_shape = shapes.title
            body_shape = shapes.placeholders[1]

            title_shape.text = '主要技术领域'
            tf = body_shape.text_frame
            tf.text = '风力发电技术'

            p = tf.add_paragraph()
            p.text = '太阳能发电技术'
            p = tf.add_paragraph()
            p.text = '智能电网技术'
            p = tf.add_paragraph()
            p.text = '储能技术'

            # 保存临时PPT文件
            with tempfile.NamedTemporaryFile(suffix='.pptx', delete=False) as temp_ppt:
                prs.save(temp_ppt.name)
                temp_ppt_path = temp_ppt.name

            try:
                # 上传PPT文件
                with open(temp_ppt_path, 'rb') as f:
                    files = {'file': ('test_presentation.pptx', f, 'application/vnd.openxmlformats-officedocument.presentationml.presentation')}
                    data = {
                        'rag_id': 'test_kb_ppt',
                        'source': 'test_ppt_presentation',
                        'split_strategy': 'hybrid',
                        'update_mode': 'incremental'
                    }

                    response = requests.post(f"{INSERT_URL}/file", files=files, data=data)
                    print(f"PPT上传状态码: {response.status_code}")
                    result = response.json()
                    print(f"PPT上传结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

                # 清理临时文件
                os.unlink(temp_ppt_path)

                return response.status_code == 200 and result.get("success", False)

            except Exception as upload_error:
                # 清理临时文件
                if os.path.exists(temp_ppt_path):
                    os.unlink(temp_ppt_path)
                raise upload_error

    except Exception as e:
        print(f"PPT测试失败: {e}")
        return False

def test_error_cases():
    """测试错误情况"""
    print("\n=== 测试错误情况 ===")
    
    # 测试空内容
    print("测试空内容...")
    try:
        response = requests.post(f"{INSERT_URL}/text", json={
            "rag_id": "test_kb_error",
            "content": ""
        })
        print(f"空内容测试 - 状态码: {response.status_code}")
        if response.status_code != 200:
            print(f"空内容测试 - 错误响应: {response.json()}")
    except Exception as e:
        print(f"空内容测试异常: {e}")
    
    # 测试缺少rag_id
    print("测试缺少rag_id...")
    try:
        response = requests.post(f"{INSERT_URL}/text", json={
            "content": "测试内容"
        })
        print(f"缺少rag_id测试 - 状态码: {response.status_code}")
        if response.status_code != 200:
            print(f"缺少rag_id测试 - 错误响应: {response.json()}")
    except Exception as e:
        print(f"缺少rag_id测试异常: {e}")
    
    # 测试不支持的文件类型
    print("测试不支持的文件类型...")
    try:
        with tempfile.NamedTemporaryFile(suffix='.unknown') as f:
            f.write(b"test content")
            f.seek(0)
            
            files = {'file': ('test.unknown', f, 'application/octet-stream')}
            data = {'rag_id': 'test_kb_error'}
            
            response = requests.post(f"{INSERT_URL}/file", files=files, data=data)
            print(f"不支持文件类型测试 - 状态码: {response.status_code}")
            if response.status_code != 200:
                print(f"不支持文件类型测试 - 错误响应: {response.json()}")
    except Exception as e:
        print(f"不支持文件类型测试异常: {e}")

def run_all_tests():
    """运行所有测试"""
    print("开始运行插入API测试...")
    print(f"测试时间: {datetime.now()}")
    print(f"测试目标: {INSERT_URL}")
    
    tests = [
        ("健康检查", test_health_check),
        ("文本内容插入", test_insert_text),
        ("全量更新模式", test_insert_text_full_update),
        ("批量插入", test_batch_insert),
        ("文件上传", test_file_upload),
        ("PPT文件支持", test_ppt_support),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"{test_name} 测试出现异常: {e}")
            results[test_name] = False
    
    # 运行错误情况测试
    test_error_cases()
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  部分测试失败，请检查服务状态和配置")

if __name__ == "__main__":
    run_all_tests()
