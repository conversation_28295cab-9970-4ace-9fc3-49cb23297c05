"""
全量更新/增量更新
update知识库中的记录
"""
from milvus import MilvusVanna
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/insert", tags=["插入数据"])


class MilvusInsert(MilvusVanna):
    def __init__(self, config=None):
        super().__init__(config)

    
# 创建插入实例
milvus_insert = MilvusInsert()

