"""
插入功能使用示例
演示如何使用深能知识库的插入API
"""
import requests
import json
import tempfile
import os

# 配置
BASE_URL = "http://localhost:3008"
INSERT_URL = f"{BASE_URL}/insert"

def example_1_insert_text():
    """示例1: 插入文本内容"""
    print("=== 示例1: 插入文本内容 ===")
    
    # 准备要插入的文本内容
    content = """
    深能集团是一家专注于清洁能源和环保技术的大型企业集团。
    
    主要业务领域包括：
    1. 风力发电：在全国多个省份建设了大型风电场
    2. 太阳能发电：拥有先进的光伏发电技术和设备
    3. 水力发电：运营多个水电站项目
    4. 环保技术：开发垃圾处理和污水处理技术
    5. 智能电网：建设智能化的电力传输和分配系统
    
    深能集团致力于推动中国的能源转型和可持续发展。
    """
    
    # 构建请求数据
    data = {
        "rag_id": "shenneng_company_info",
        "content": content,
        "source": "company_introduction",
        "split_strategy": "hybrid",
        "update_mode": "incremental"
    }
    
    try:
        response = requests.post(f"{INSERT_URL}/text", json=data)
        result = response.json()
        
        print(f"请求状态: {response.status_code}")
        print(f"插入结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get("success"):
            print(f"✅ 成功插入 {result['chunks_count']} 个文本块")
            print(f"📄 文档ID: {result['doc_id']}")
        else:
            print(f"❌ 插入失败: {result.get('message')}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def example_2_upload_file():
    """示例2: 上传文件"""
    print("\n=== 示例2: 上传文件 ===")
    
    # 创建一个示例文档
    document_content = """
    深能集团技术创新报告
    
    一、研发投入
    深能集团每年投入营收的8%用于技术研发，建立了多个研发中心。
    
    二、核心技术
    1. 海上风电技术
       - 大型海上风机设计
       - 海上风电场建设技术
       - 海洋环境适应性技术
    
    2. 智能光伏技术
       - 高效太阳能电池板
       - 智能跟踪系统
       - 储能集成技术
    
    3. 智慧能源管理
       - 能源大数据分析
       - 智能调度系统
       - 预测性维护技术
    
    三、未来规划
    深能集团计划在未来5年内，在新能源技术领域投资100亿元，
    重点发展氢能、储能和智能电网技术。
    """
    
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(document_content)
            temp_file_path = f.name
        
        # 上传文件
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('shenneng_tech_report.txt', f, 'text/plain')}
            data = {
                'rag_id': 'shenneng_tech_info',
                'source': 'technology_innovation_report',
                'split_strategy': 'hybrid',
                'update_mode': 'incremental'
            }
            
            response = requests.post(f"{INSERT_URL}/file", files=files, data=data)
            result = response.json()
            
            print(f"请求状态: {response.status_code}")
            print(f"上传结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get("success"):
                print(f"✅ 成功上传并插入 {result['chunks_count']} 个文本块")
                print(f"📄 文档ID: {result['doc_id']}")
            else:
                print(f"❌ 上传失败: {result.get('message')}")
        
        # 清理临时文件
        os.unlink(temp_file_path)
        
    except Exception as e:
        print(f"❌ 文件上传失败: {e}")

def example_3_batch_insert():
    """示例3: 批量插入"""
    print("\n=== 示例3: 批量插入 ===")
    
    # 准备批量数据
    batch_data = [
        {
            "rag_id": "shenneng_projects",
            "content": """
            深能海上风电项目
            
            项目概况：
            - 项目名称：深能江苏如东海上风电场
            - 装机容量：300MW
            - 风机数量：60台
            - 项目投资：45亿元
            
            技术特点：
            - 采用5MW大型海上风机
            - 配备先进的海缆传输系统
            - 具备远程监控和智能运维能力
            
            项目效益：
            年发电量约8亿千瓦时，可满足30万户家庭用电需求。
            """,
            "source": "offshore_wind_project",
            "split_strategy": "semantic"
        },
        {
            "rag_id": "shenneng_projects", 
            "content": """
            深能光伏发电项目
            
            项目概况：
            - 项目名称：深能新疆哈密光伏电站
            - 装机容量：200MW
            - 占地面积：500公顷
            - 项目投资：16亿元
            
            技术特点：
            - 采用高效单晶硅组件
            - 配备智能跟踪系统
            - 集成储能系统
            
            环保效益：
            年减少二氧化碳排放约20万吨，相当于植树造林5000亩。
            """,
            "source": "solar_power_project",
            "split_strategy": "hybrid"
        },
        {
            "rag_id": "shenneng_projects",
            "content": """
            深能智慧园区项目
            
            项目概况：
            - 项目名称：深能绿色智慧产业园
            - 园区面积：1000亩
            - 入驻企业：50家
            - 项目投资：30亿元
            
            智慧特色：
            - 分布式能源系统
            - 智能楼宇管理
            - 绿色交通体系
            - 环境监测网络
            
            创新亮点：
            园区实现了100%清洁能源供应，成为国家级绿色园区示范项目。
            """,
            "source": "smart_park_project",
            "split_strategy": "fixed"
        }
    ]
    
    try:
        response = requests.post(f"{INSERT_URL}/batch", json=batch_data)
        result = response.json()
        
        print(f"请求状态: {response.status_code}")
        print(f"批量插入结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if result.get("success"):
            batch_result = result["result"]
            print(f"✅ 批量插入完成")
            print(f"📊 总计: {batch_result['total']} 项")
            print(f"✅ 成功: {batch_result['success']} 项")
            print(f"❌ 失败: {batch_result['failed']} 项")
            
            # 显示详细结果
            for detail in batch_result["details"]:
                status = "✅" if detail["success"] else "❌"
                print(f"   {status} 项目 {detail['index'] + 1}: {detail['message']}")
        else:
            print(f"❌ 批量插入失败: {result.get('message')}")
            
    except Exception as e:
        print(f"❌ 批量插入请求失败: {e}")

def example_4_full_update():
    """示例4: 全量更新模式"""
    print("\n=== 示例4: 全量更新模式 ===")
    
    doc_id = "company_policy_001"
    
    # 第一次插入
    print("第一次插入...")
    data1 = {
        "rag_id": "shenneng_policies",
        "doc_id": doc_id,
        "content": "深能集团环保政策（第一版）：我们承诺减少碳排放，推广清洁能源。",
        "source": "environmental_policy_v1",
        "update_mode": "full"
    }
    
    try:
        response1 = requests.post(f"{INSERT_URL}/text", json=data1)
        result1 = response1.json()
        print(f"第一次插入结果: {result1.get('message')}")
        
        # 第二次插入（全量更新）
        print("\n第二次插入（全量更新）...")
        data2 = {
            "rag_id": "shenneng_policies",
            "doc_id": doc_id,
            "content": """
            深能集团环保政策（第二版）
            
            1. 碳中和目标：2030年实现碳达峰，2050年实现碳中和
            2. 清洁能源：新增投资100%投向清洁能源项目
            3. 绿色运营：办公场所100%使用清洁能源
            4. 生态保护：每年投入1%营收用于生态修复
            5. 技术创新：加大绿色技术研发投入
            """,
            "source": "environmental_policy_v2",
            "update_mode": "full"
        }
        
        response2 = requests.post(f"{INSERT_URL}/text", json=data2)
        result2 = response2.json()
        print(f"第二次插入结果: {result2.get('message')}")
        
        if result1.get("success") and result2.get("success"):
            print("✅ 全量更新演示完成")
            print(f"📄 文档ID: {doc_id}")
            print("💡 第一版内容已被第二版完全替换")
        
    except Exception as e:
        print(f"❌ 全量更新演示失败: {e}")

def check_service_health():
    """检查服务健康状态"""
    print("=== 检查服务状态 ===")
    try:
        response = requests.get(f"{INSERT_URL}/health", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 插入服务运行正常")
            print(f"🕐 服务时间: {result.get('timestamp')}")
            return True
        else:
            print(f"❌ 插入服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到插入服务: {e}")
        print(f"💡 请确保服务已启动: python app.py")
        return False

def main():
    """主函数"""
    print("🚀 深能知识库插入功能演示")
    print("=" * 50)
    
    # 检查服务状态
    if not check_service_health():
        print("\n⚠️  服务未运行，请先启动服务后再运行示例")
        return
    
    print("\n开始演示各种插入功能...")
    
    # 运行示例
    example_1_insert_text()
    example_2_upload_file()
    example_3_batch_insert()
    example_4_full_update()
    
    print("\n" + "=" * 50)
    print("🎉 所有示例演示完成！")
    print("\n💡 提示:")
    print("- 可以通过查询API验证插入的数据")
    print("- 支持的文件类型: txt, pdf, doc, docx, xls, xlsx, csv, ppt, pptx, md, markdown")
    print("- 推荐使用 hybrid 分块策略获得最佳效果")
    print("- 增量更新可避免重复插入相同内容")
    print("- PPT文件会自动提取幻灯片内容、表格和图表标题")
    print("- Markdown文件会保留结构化信息并转换为纯文本")

if __name__ == "__main__":
    main()
