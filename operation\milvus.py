"""
milvus类的定义
"""
from pymilvus import DataType, connections, MilvusClient, Function, FunctionType
from sentence_transformers import SentenceTransformer
from openai import OpenAI
from config import EMBEDDING_MODEL_PATH, MILVUS_HOST, MILVUS_PORT,\
    LLM_API_KEY, LLM_BASE_URL, LLM_MODEL_NAME, vector_dim
from config import collection_name

class MilvusVanna:
    def __init__(self, config=None):
        config = config or {}

        # 初始化本地嵌入模型
        try:
            embedding_model_path = config.get("embedding_model_path", EMBEDDING_MODEL_PATH)
            self.embedding_model = SentenceTransformer(embedding_model_path)
            print(f"Loaded SentenceTransformer model: {embedding_model_path}")
        except Exception as e:
            print(f"Failed to load SentenceTransformer model: {e}")
            raise

        self.host = config.get("host", MILVUS_HOST)
        self.port = config.get("port", MILVUS_PORT)
        self.vector_dim = config.get("vector_dim", vector_dim)
        self.collection_name = config.get("collection_name", collection_name)

        # 初始化 Milvus 连接
        connections.connect(host=self.host, port=self.port)

        # LLM 配置
        self.llm_api_key = config.get("llm_api_key", LLM_API_KEY)
        self.llm_base_url = config.get("llm_base_url", LLM_BASE_URL)
        self.llm_model_name = config.get("llm_model_name", LLM_MODEL_NAME)
        self.client = OpenAI(api_key=self.llm_api_key, base_url=self.llm_base_url)
        self.config = config

    def _get_or_create_collection(self, name: str):
        # 用 MilvusClient 新 API 创建/获取 collection
        client = MilvusClient(uri=f"http://{self.host}:{self.port}")
        if name not in client.list_collections():
            schema = client.create_schema()
            schema.add_field("id", DataType.VARCHAR, max_length=128, is_primary=True)
            # 文本分块后的内容
            schema.add_field("content", DataType.VARCHAR, max_length=65535, enable_analyzer=True)
            # 知识库的id
            schema.add_field('rag_id', DataType.VARCHAR, max_length=128)
            # 文件来源
            schema.add_field("source", DataType.VARCHAR, max_length=128)
            # 上传文件的id
            schema.add_field("doc_id", DataType.VARCHAR, max_length=128)
            # 分块的id
            schema.add_field("chunk_id", DataType.VARCHAR, max_length=128)
            # dense:理解用户意图，找到语义相关内容
            schema.add_field("dense", DataType.FLOAT_VECTOR, dim=self.vector_dim)
            # 精确匹配关键词，确保相关性
            schema.add_field("sparse", DataType.SPARSE_FLOAT_VECTOR)
            schema.add_field("content_hash", DataType.VARCHAR, max_length=32)

            bm25_func = Function(
                name="content_bm25_emb",
                input_field_names=["content"],
                output_field_names=["sparse"],
                function_type=FunctionType.BM25,
            )
            schema.add_function(bm25_func)

            index_params = client.prepare_index_params()
            index_params.add_index(
                field_name="dense",
                index_type="AUTOINDEX",
                metric_type="IP"
            )
            index_params.add_index(
                field_name="sparse",
                index_type="SPARSE_INVERTED_INDEX",
                metric_type="BM25"
            )
            index_params.add_index(
                field_name="id",
                index_type="INVERTED"
            )
            index_params.add_index(
                field_name="content_hash",
                index_type="INVERTED"
            )

            client.create_collection(
                collection_name=name,
                schema=schema,
                index_params=index_params
            )
        return client
    
    def query(self, query_text: str, rag_ids: list, limit: int = 10):
        """
        执行混合检索查询，根据用户查询和RAG ID列表，从Milvus中检索相关内容。
        
        Args:
            query_text (str): 用户输入的查询文本
            rag_ids (list): RAG ID列表，用于过滤数据
            limit (int): 返回结果的最大数量，默认为10
        
        Returns:
            list: 包含检索结果的列表，每个结果包括id, content, rag_id, source, chunk_id和分数
        """
        # 获取或创建collection
        client = self._get_or_create_collection(collection_name)
        
        try:
            # 生成查询的dense embedding
            query_embedding = self.embedding_model.encode([query_text])[0]
            
            # 构建混合检索查询
            search_params = {
                "data": [query_embedding],  # dense vector
                "anns_field": "dense",      # 目标字段为dense vector
                "param": {
                    "metric_type": "IP",    # Inner Product作为dense vector的度量
                    "params": {}
                },
                "limit": limit,             # 返回的最大结果数
                "expr": f"rag_id in {rag_ids}",  # 过滤条件：只查询指定rag_ids的数据
                "output_fields": ["id", "content", "rag_id", "source", "chunk_id"]  # 返回的字段
            }
            
            # 添加稀疏向量检索（BM25）
            hybrid_search_params = {
                "sparse": {
                    "anns_field": "sparse",
                    "param": {
                        "metric_type": "BM25",
                        "params": {}
                    },
                    "data": [query_text],  # 直接使用查询文本进行BM25检索
                    "weight": 0.5          # 稀疏向量权重
                },
                "dense": search_params,
                "weight": 0.5              # 稠密向量权重
            }
            
            # 执行混合检索
            results = client.hybrid_search(
                collection_name=collection_name,
                **hybrid_search_params
            )
            
            # 格式化结果
            formatted_results = []
            for result in results[0]:  # hybrid_search返回结果为列表的列表
                formatted_results.append({
                    "id": result["id"],
                    "content": result["content"],
                    "rag_id": result["rag_id"],
                    "source": result["source"],
                    "doc_id": result["doc_id"],
                    "chunk_id": result["chunk_id"],
                    "score": result["distance"]  # 混合检索的分数
                })
            
            return formatted_results
        
        except Exception as e:
            print(f"Query failed: {e}")
            raise
        finally:
            client.close()  # 关闭 Milvus 客户端连接

