# 删除记录 API 使用说明

## 概述

知识库系统支持多种级别的删除操作，可以按照不同的维度删除记录：

- **知识库级别**: 删除整个知识库的所有记录 (`rag_id`)
- **文档级别**: 删除指定文档的所有记录 (`doc_id`)  
- **分块级别**: 删除指定的文本分块 (`chunk_id`)
- **记录级别**: 删除指定的记录 (`record_id`)

## API 端点

### 1. 统一删除接口

**POST** `/delete/`

支持批量删除和多种删除方式的组合使用。

#### 请求体格式

```json
{
  "rag_ids": ["rag_001", "rag_002"],           // 可选：知识库ID列表
  "doc_ids": ["doc_001", "doc_002"],           // 可选：文档ID列表  
  "chunk_ids": ["chunk_001", "chunk_002"],     // 可选：分块ID列表
  "record_ids": ["record_001", "record_002"]   // 可选：记录ID列表
}
```

#### 响应格式

```json
{
  "success": true,
  "deleted_count": 150,
  "message": "成功删除 150 条记录",
  "details": {
    "rag_ids_deleted": 100,
    "doc_ids_deleted": 30,
    "chunk_ids_deleted": 15,
    "record_ids_deleted": 5
  }
}
```

### 2. 便捷删除接口

#### 按知识库删除
**DELETE** `/delete/rag/{rag_id}`

删除指定知识库的所有记录。

#### 按文档删除  
**DELETE** `/delete/doc/{doc_id}`

删除指定文档的所有记录。

#### 按分块删除
**DELETE** `/delete/chunk/{chunk_id}`

删除指定的文本分块。

#### 按记录删除
**DELETE** `/delete/record/{record_id}`

删除指定的记录。

## 使用示例

### 示例 1: 删除整个知识库

```bash
# 删除单个知识库
curl -X DELETE "http://localhost:3008/delete/rag/rag_001"

# 批量删除多个知识库
curl -X POST "http://localhost:3008/delete/" \
  -H "Content-Type: application/json" \
  -d '{
    "rag_ids": ["rag_001", "rag_002", "rag_003"]
  }'
```

### 示例 2: 删除指定文档

```bash
# 删除单个文档
curl -X DELETE "http://localhost:3008/delete/doc/doc_001"

# 批量删除多个文档
curl -X POST "http://localhost:3008/delete/" \
  -H "Content-Type: application/json" \
  -d '{
    "doc_ids": ["doc_001", "doc_002"]
  }'
```

### 示例 3: 删除指定分块

```bash
# 删除单个分块
curl -X DELETE "http://localhost:3008/delete/chunk/chunk_001"

# 批量删除多个分块
curl -X POST "http://localhost:3008/delete/" \
  -H "Content-Type: application/json" \
  -d '{
    "chunk_ids": ["chunk_001", "chunk_002", "chunk_003"]
  }'
```

### 示例 4: 混合删除操作

```bash
# 同时删除知识库、文档和分块
curl -X POST "http://localhost:3008/delete/" \
  -H "Content-Type: application/json" \
  -d '{
    "rag_ids": ["rag_001"],
    "doc_ids": ["doc_002", "doc_003"],
    "chunk_ids": ["chunk_004", "chunk_005"]
  }'
```

### 示例 5: Python 客户端调用

```python
import requests

# 删除配置
delete_url = "http://localhost:3008/delete/"

# 批量删除请求
delete_request = {
    "rag_ids": ["rag_001", "rag_002"],
    "doc_ids": ["doc_003"],
    "chunk_ids": ["chunk_004", "chunk_005"]
}

# 发送删除请求
response = requests.post(delete_url, json=delete_request)

if response.status_code == 200:
    result = response.json()
    print(f"删除成功: {result['message']}")
    print(f"删除详情: {result['details']}")
else:
    print(f"删除失败: {response.text}")
```

## 错误处理

### 常见错误码

- **400 Bad Request**: 请求参数错误，至少需要提供一种删除条件
- **500 Internal Server Error**: 服务器内部错误，删除操作失败

### 错误响应示例

```json
{
  "success": false,
  "deleted_count": 0,
  "message": "删除失败: 连接Milvus失败",
  "details": {}
}
```

## 注意事项

1. **删除操作不可逆**: 一旦删除，数据无法恢复，请谨慎操作
2. **批量删除**: 支持同时使用多种删除条件，系统会按顺序执行
3. **性能考虑**: 大批量删除可能需要较长时间，建议分批处理
4. **权限控制**: 建议在生产环境中添加适当的权限验证
5. **日志记录**: 所有删除操作都会记录在系统日志中

## 数据结构说明

### Milvus 集合字段

- `id`: 记录的唯一标识符
- `rag_id`: 知识库ID
- `doc_id`: 文档ID  
- `chunk_id`: 分块ID
- `content`: 文本内容
- `source`: 文件来源
- `dense`: 稠密向量
- `sparse`: 稀疏向量
- `content_hash`: 内容哈希值

### 删除逻辑

- 按 `rag_id` 删除: 删除该知识库下的所有文档和分块
- 按 `doc_id` 删除: 删除该文档下的所有分块
- 按 `chunk_id` 删除: 删除指定的文本分块
- 按 `record_id` 删除: 删除指定的记录

## 监控和日志

系统会记录以下信息：

- 删除操作的时间戳
- 删除的记录数量
- 删除条件和参数
- 操作结果和错误信息

日志文件位置: `logs/app.log`
