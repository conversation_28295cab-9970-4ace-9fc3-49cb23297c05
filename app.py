from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import logging
import sys
import os
from logging.handlers import TimedRotatingFileHandler
from config import PORT
from operation.query import router as query_router
from operation.delete import router as delete_router

# 日志配置
log_file = "logs/app.log"  # 日志文件路径
os.makedirs(os.path.dirname(log_file), exist_ok=True)

# 日志每天轮转，保留七天
file_handler = TimedRotatingFileHandler(
    log_file, when="midnight", interval=1, backupCount=7, encoding="utf-8"
)
file_handler.suffix = "%Y-%m-%d"
formatter = logging.Formatter('%(asctime)s %(levelname)s %(name)s %(message)s')
file_handler.setFormatter(formatter)

console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(formatter)

logging.basicConfig(
    level=logging.INFO,
    handlers=[file_handler, console_handler]
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="深能知识库 V.1.0")

# 跨域配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 可根据需要指定允许的源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 注册路由
app.include_router(query_router)
app.include_router(delete_router)

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled error: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal Server Error"}
    )

# 示例根接口
@app.get("/")
async def root():
    return {"message": "FastAPI app is running."}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app:app", port=PORT, reload=True)