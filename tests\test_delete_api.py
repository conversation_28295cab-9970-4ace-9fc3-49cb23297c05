"""
删除API功能测试
"""
import pytest
import requests
import json
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:3008"
DELETE_URL = f"{BASE_URL}/delete"

class TestDeleteAPI:
    """删除API测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.test_data = {
            "rag_ids": ["test_rag_001", "test_rag_002"],
            "doc_ids": ["test_doc_001", "test_doc_002"],
            "chunk_ids": ["test_chunk_001", "test_chunk_002"],
            "record_ids": ["test_record_001", "test_record_002"]
        }
    
    def test_delete_by_rag_ids(self):
        """测试按知识库ID删除"""
        payload = {"rag_ids": self.test_data["rag_ids"]}
        response = requests.post(f"{DELETE_URL}/", json=payload)
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "deleted_count" in result
        assert "rag_ids_deleted" in result["details"]
    
    def test_delete_by_doc_ids(self):
        """测试按文档ID删除"""
        payload = {"doc_ids": self.test_data["doc_ids"]}
        response = requests.post(f"{DELETE_URL}/", json=payload)
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "deleted_count" in result
        assert "doc_ids_deleted" in result["details"]
    
    def test_delete_by_chunk_ids(self):
        """测试按分块ID删除"""
        payload = {"chunk_ids": self.test_data["chunk_ids"]}
        response = requests.post(f"{DELETE_URL}/", json=payload)
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "deleted_count" in result
        assert "chunk_ids_deleted" in result["details"]
    
    def test_delete_by_record_ids(self):
        """测试按记录ID删除"""
        payload = {"record_ids": self.test_data["record_ids"]}
        response = requests.post(f"{DELETE_URL}/", json=payload)
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "deleted_count" in result
        assert "record_ids_deleted" in result["details"]
    
    def test_mixed_delete(self):
        """测试混合删除"""
        payload = {
            "rag_ids": ["test_rag_003"],
            "doc_ids": ["test_doc_003"],
            "chunk_ids": ["test_chunk_003"]
        }
        response = requests.post(f"{DELETE_URL}/", json=payload)
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
        assert "deleted_count" in result
    
    def test_delete_single_rag(self):
        """测试单个知识库删除便捷接口"""
        rag_id = "test_rag_single"
        response = requests.delete(f"{DELETE_URL}/rag/{rag_id}")
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
    
    def test_delete_single_doc(self):
        """测试单个文档删除便捷接口"""
        doc_id = "test_doc_single"
        response = requests.delete(f"{DELETE_URL}/doc/{doc_id}")
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
    
    def test_delete_single_chunk(self):
        """测试单个分块删除便捷接口"""
        chunk_id = "test_chunk_single"
        response = requests.delete(f"{DELETE_URL}/chunk/{chunk_id}")
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
    
    def test_delete_single_record(self):
        """测试单个记录删除便捷接口"""
        record_id = "test_record_single"
        response = requests.delete(f"{DELETE_URL}/record/{record_id}")
        
        assert response.status_code == 200
        result = response.json()
        assert result["success"] is True
    
    def test_empty_request(self):
        """测试空请求"""
        payload = {}
        response = requests.post(f"{DELETE_URL}/", json=payload)
        
        assert response.status_code == 400
        assert "至少需要提供一种删除条件" in response.json()["detail"]
    
    def test_invalid_request(self):
        """测试无效请求"""
        payload = {
            "rag_ids": [],
            "doc_ids": [],
            "chunk_ids": [],
            "record_ids": []
        }
        response = requests.post(f"{DELETE_URL}/", json=payload)
        
        assert response.status_code == 400

def manual_test_delete_api():
    """手动测试删除API功能"""
    print("开始测试删除API功能...")
    
    # 测试数据
    test_cases = [
        {
            "name": "按知识库ID删除",
            "payload": {"rag_ids": ["manual_test_rag_001"]},
            "url": f"{DELETE_URL}/"
        },
        {
            "name": "按文档ID删除", 
            "payload": {"doc_ids": ["manual_test_doc_001"]},
            "url": f"{DELETE_URL}/"
        },
        {
            "name": "按分块ID删除",
            "payload": {"chunk_ids": ["manual_test_chunk_001"]},
            "url": f"{DELETE_URL}/"
        },
        {
            "name": "混合删除",
            "payload": {
                "rag_ids": ["manual_test_rag_002"],
                "doc_ids": ["manual_test_doc_002"]
            },
            "url": f"{DELETE_URL}/"
        }
    ]
    
    # 便捷接口测试
    convenience_tests = [
        {
            "name": "便捷接口-删除知识库",
            "method": "DELETE",
            "url": f"{DELETE_URL}/rag/convenience_test_rag"
        },
        {
            "name": "便捷接口-删除文档",
            "method": "DELETE", 
            "url": f"{DELETE_URL}/doc/convenience_test_doc"
        },
        {
            "name": "便捷接口-删除分块",
            "method": "DELETE",
            "url": f"{DELETE_URL}/chunk/convenience_test_chunk"
        }
    ]
    
    # 执行POST请求测试
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        try:
            response = requests.post(test_case["url"], json=test_case["payload"])
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        except Exception as e:
            print(f"测试失败: {str(e)}")
    
    # 执行DELETE请求测试
    for test_case in convenience_tests:
        print(f"\n测试: {test_case['name']}")
        try:
            response = requests.delete(test_case["url"])
            print(f"状态码: {response.status_code}")
            print(f"响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        except Exception as e:
            print(f"测试失败: {str(e)}")
    
    print("\n删除API测试完成!")

if __name__ == "__main__":
    # 运行手动测试
    manual_test_delete_api()
