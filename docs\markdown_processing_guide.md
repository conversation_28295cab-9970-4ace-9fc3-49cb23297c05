# Markdown 文件处理指南

## 概述

深能知识库系统现已支持 Markdown 文档的自动处理和内容提取，可以将 `.md` 和 `.markdown` 文件中的结构化内容智能提取并存储到知识库中。

## 支持的文件格式

- **Markdown** (`.md`)
- **Markdown** (`.markdown`)

## 功能特性

### 1. 智能内容提取

系统会自动处理以下Markdown元素：

- **标题结构**: H1-H6标题，保留层级关系
- **文本格式**: 粗体、斜体、删除线等格式标记
- **列表内容**: 有序列表、无序列表、任务列表
- **表格数据**: 表格内容按行列结构组织
- **代码块**: 代码内容提取（移除语法高亮标记）
- **引用块**: 引用内容提取
- **链接文本**: 保留链接文本，移除URL
- **图片描述**: 提取图片的alt文本

### 2. 内容处理方式

#### 2.1 格式标记处理

原始Markdown：
```markdown
# 主标题

## 二级标题

这是**粗体文本**和*斜体文本*。

### 列表示例

1. 第一项
2. 第二项
   - 嵌套项目
   - 另一个嵌套项目
```

处理后的文本：
```
主标题

二级标题

这是粗体文本和斜体文本。

列表示例

• 第一项
• 第二项
• 嵌套项目
• 另一个嵌套项目
```

#### 2.2 表格处理

原始Markdown表格：
```markdown
| 技术类型 | 装机容量 | 年发电量 |
|---------|---------|---------|
| 风力发电 | 2000MW | 40亿kWh |
| 太阳能发电 | 1500MW | 25亿kWh |
```

处理后的文本：
```
技术类型 | 装机容量 | 年发电量
风力发电 | 2000MW | 40亿kWh
太阳能发电 | 1500MW | 25亿kWh
```

#### 2.3 代码块处理

原始Markdown：
```markdown
```python
def calculate_power(voltage, current):
    return voltage * current
```
```

处理后的文本：
```
def calculate_power(voltage, current):
    return voltage * current
```

## 使用方法

### 1. API 调用

#### 上传 Markdown 文件

```bash
curl -X POST "http://localhost:3008/insert/file" \
  -F "file=@document.md" \
  -F "rag_id=knowledge_base_id" \
  -F "source=technical_documentation" \
  -F "split_strategy=semantic" \
  -F "update_mode=incremental"
```

#### Python 客户端

```python
import requests

def upload_markdown(file_path, rag_id, source=None):
    url = "http://localhost:3008/insert/file"
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {
            'rag_id': rag_id,
            'source': source or file_path,
            'split_strategy': 'semantic',  # 推荐用于技术文档
            'update_mode': 'incremental'
        }
        
        response = requests.post(url, files=files, data=data)
        return response.json()

# 使用示例
result = upload_markdown("tech_doc.md", "tech_kb", "technical_guide")
print(result)
```

### 2. 响应格式

成功上传后的响应：

```json
{
  "success": true,
  "message": "成功插入 12 个文本块",
  "doc_id": "auto_generated_uuid",
  "chunks_count": 12,
  "details": {
    "split_strategy": "semantic",
    "update_mode": "incremental",
    "source": "technical_documentation.md"
  }
}
```

## 文本分块策略

Markdown 文件特别适合不同的分块策略：

### 1. 语义分块 (semantic) - 推荐
- 按内容语义相似度分割
- 保持相关技术概念的完整性
- 适合技术文档、API文档、教程

### 2. 混合分块 (hybrid)
- 先按语义分块，再按固定大小调整
- 平衡内容连贯性和检索效果
- 适合长篇技术白皮书、规范文档

### 3. 固定分块 (fixed)
- 按固定字符数分割
- 确保文本块大小均匀
- 适合结构化的配置文档、参考手册

## 最佳实践

### 1. 文档准备

- **清晰结构**: 使用标准的Markdown标题层级
- **完整表格**: 确保表格格式正确，包含表头
- **代码注释**: 在代码块中添加必要的注释
- **链接文本**: 使用描述性的链接文本

### 2. 上传策略

- **技术文档**: 使用 `semantic` 分块策略
- **API文档**: 使用 `hybrid` 分块策略
- **配置文档**: 使用 `fixed` 分块策略
- **增量更新**: 文档修订时使用 `incremental` 模式

### 3. 内容优化

- **标题层级**: 保持清晰的标题层级结构
- **段落长度**: 控制段落长度，便于分块
- **表格设计**: 使用简洁的表格结构
- **代码示例**: 提供完整的代码示例

## 应用场景

### 1. 技术文档

- **API文档**: 接口说明、参数定义、示例代码
- **用户手册**: 产品使用指南、操作步骤
- **开发文档**: 架构设计、技术规范

### 2. 项目文档

- **README文件**: 项目介绍、安装指南
- **变更日志**: 版本更新记录
- **贡献指南**: 开发规范、提交流程

### 3. 知识库

- **技术博客**: 技术分享、经验总结
- **学习笔记**: 培训材料、学习记录
- **规范文档**: 编码规范、设计规范

## 处理限制

### 1. 不支持的功能

- **数学公式**: LaTeX数学公式不会被特殊处理
- **图表渲染**: Mermaid等图表语法作为文本处理
- **HTML标签**: 复杂的HTML标签会被移除
- **脚注**: 脚注引用不会被特殊处理

### 2. 格式限制

- **嵌套表格**: 不支持复杂的嵌套表格
- **多级引用**: 深层嵌套的引用可能格式化异常
- **特殊字符**: 某些特殊Unicode字符可能处理异常

## 故障排除

### 1. 常见问题

**问题**: 表格内容提取不完整
**解决**: 检查表格格式，确保使用标准Markdown表格语法

**问题**: 代码块格式混乱
**解决**: 确保代码块使用正确的三个反引号标记

**问题**: 中文内容乱码
**解决**: 确保Markdown文件使用UTF-8编码保存

### 2. 调试方法

1. **检查编码**: 确认文件使用UTF-8编码
2. **验证语法**: 使用Markdown编辑器验证语法正确性
3. **测试上传**: 使用简单的Markdown文件进行测试
4. **查看日志**: 检查服务器日志获取详细错误信息

## 性能优化

### 1. 文件大小

- **建议大小**: 单个Markdown文件不超过10MB
- **分割策略**: 大文档建议按章节分割为多个文件
- **图片处理**: 避免在Markdown中嵌入大量图片

### 2. 处理效率

- **批量上传**: 使用批量接口处理多个文档
- **增量更新**: 只更新修改的文档部分
- **缓存机制**: 利用内容哈希避免重复处理

## 示例代码

完整的Markdown处理示例请参考：
- `examples/markdown_processing_example.py` - 完整演示代码
- `tests/test_insert_api.py` - 测试用例

## 技术实现

### 1. 处理流程

1. **文件读取**: 使用UTF-8编码读取Markdown文件
2. **语法解析**: 识别Markdown语法元素
3. **格式转换**: 将Markdown格式转换为纯文本
4. **内容清理**: 移除多余的空行和格式标记
5. **文本分块**: 根据策略进行智能分块
6. **向量化**: 生成文本向量表示
7. **存储**: 保存到Milvus向量数据库

### 2. 正则表达式处理

系统使用多个正则表达式来处理不同的Markdown元素：

```python
# 标题处理
content = re.sub(r'^#{1,6}\s+(.+)$', r'\1', content, flags=re.MULTILINE)

# 代码块处理
content = re.sub(r'```[\w]*\n(.*?)\n```', r'\1', content, flags=re.DOTALL)

# 链接处理
content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)
```

## 更新日志

- **v1.0**: 基础Markdown文件支持
- **v1.1**: 添加表格内容提取
- **v1.2**: 优化代码块处理
- **v1.3**: 改进列表格式化
- **v1.4**: 增强中文内容支持

## 技术支持

如遇到Markdown处理相关问题，请：

1. 检查文件编码格式
2. 验证Markdown语法
3. 查看处理日志
4. 联系技术支持团队
