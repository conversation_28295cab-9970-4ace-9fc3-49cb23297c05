"""
PPT文件处理示例
演示如何处理PowerPoint文件并插入到知识库
"""
import requests
import json
import tempfile
import os

try:
    from pptx import Presentation
    from pptx.util import Inches
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False
    print("⚠️  python-pptx 未安装，请运行: pip install python-pptx")

# 配置
BASE_URL = "http://localhost:3008"
INSERT_URL = f"{BASE_URL}/insert"

def create_sample_ppt():
    """创建一个示例PPT文件"""
    if not PPTX_AVAILABLE:
        return None
    
    print("📝 创建示例PPT文件...")
    
    # 创建演示文稿
    prs = Presentation()
    
    # 幻灯片1: 标题页
    title_slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(title_slide_layout)
    title = slide.shapes.title
    subtitle = slide.placeholders[1]
    
    title.text = "深能集团清洁能源技术报告"
    subtitle.text = "2025年技术发展规划与展望"
    
    # 幻灯片2: 公司概况
    bullet_slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(bullet_slide_layout)
    shapes = slide.shapes
    
    title_shape = shapes.title
    body_shape = shapes.placeholders[1]
    
    title_shape.text = '深能集团概况'
    tf = body_shape.text_frame
    tf.text = '成立于2008年，专注清洁能源领域'
    
    p = tf.add_paragraph()
    p.text = '业务覆盖风电、光伏、储能、智能电网'
    p = tf.add_paragraph()
    p.text = '累计装机容量超过5000MW'
    p = tf.add_paragraph()
    p.text = '服务客户遍布全国30个省市'
    
    # 幻灯片3: 技术优势
    slide = prs.slides.add_slide(bullet_slide_layout)
    shapes = slide.shapes
    
    title_shape = shapes.title
    body_shape = shapes.placeholders[1]
    
    title_shape.text = '核心技术优势'
    tf = body_shape.text_frame
    tf.text = '海上风电技术：自主研发大型海上风机'
    
    p = tf.add_paragraph()
    p.text = '智能光伏技术：AI驱动的发电效率优化'
    p = tf.add_paragraph()
    p.text = '储能技术：锂电池+氢能混合储能系统'
    p = tf.add_paragraph()
    p.text = '智慧运维：基于大数据的预测性维护'
    
    # 幻灯片4: 项目案例
    slide = prs.slides.add_slide(bullet_slide_layout)
    shapes = slide.shapes
    
    title_shape = shapes.title
    body_shape = shapes.placeholders[1]
    
    title_shape.text = '重点项目案例'
    tf = body_shape.text_frame
    tf.text = '江苏如东海上风电场 - 300MW装机容量'
    
    p = tf.add_paragraph()
    p.text = '新疆哈密光伏电站 - 200MW装机容量'
    p = tf.add_paragraph()
    p.text = '深圳智慧园区 - 分布式能源示范项目'
    p = tf.add_paragraph()
    p.text = '青海储能电站 - 100MWh储能系统'
    
    # 幻灯片5: 未来规划
    slide = prs.slides.add_slide(bullet_slide_layout)
    shapes = slide.shapes
    
    title_shape = shapes.title
    body_shape = shapes.placeholders[1]
    
    title_shape.text = '2025-2030发展规划'
    tf = body_shape.text_frame
    tf.text = '目标：新增装机容量3000MW'
    
    p = tf.add_paragraph()
    p.text = '重点发展海上风电和分布式光伏'
    p = tf.add_paragraph()
    p.text = '加大储能技术研发投入'
    p = tf.add_paragraph()
    p.text = '推进数字化转型和智慧运营'
    
    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.pptx', delete=False)
    prs.save(temp_file.name)
    temp_file.close()
    
    print(f"✅ 示例PPT文件已创建: {temp_file.name}")
    return temp_file.name

def upload_ppt_to_knowledge_base(ppt_file_path, rag_id="shenneng_tech_ppt"):
    """上传PPT文件到知识库"""
    print(f"\n📤 上传PPT文件到知识库...")
    
    try:
        with open(ppt_file_path, 'rb') as f:
            files = {'file': ('shenneng_tech_report.pptx', f, 'application/vnd.openxmlformats-officedocument.presentationml.presentation')}
            data = {
                'rag_id': rag_id,
                'source': 'shenneng_tech_presentation_2025',
                'split_strategy': 'hybrid',
                'update_mode': 'incremental'
            }
            
            response = requests.post(f"{INSERT_URL}/file", files=files, data=data)
            result = response.json()
            
            print(f"上传状态: {response.status_code}")
            print(f"上传结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get("success"):
                print(f"✅ PPT文件上传成功!")
                print(f"📄 文档ID: {result['doc_id']}")
                print(f"📊 生成文本块数量: {result['chunks_count']}")
                return True
            else:
                print(f"❌ PPT文件上传失败: {result.get('message')}")
                return False
                
    except Exception as e:
        print(f"❌ PPT文件上传异常: {e}")
        return False

def demonstrate_ppt_content_extraction():
    """演示PPT内容提取功能"""
    print("\n🔍 演示PPT内容提取功能...")
    
    if not PPTX_AVAILABLE:
        print("❌ 需要安装 python-pptx 库")
        return False
    
    # 创建示例PPT
    ppt_path = create_sample_ppt()
    if not ppt_path:
        return False
    
    try:
        # 手动演示内容提取过程
        print("\n📖 PPT内容预览:")
        prs = Presentation(ppt_path)
        
        for slide_num, slide in enumerate(prs.slides, 1):
            print(f"\n--- 幻灯片 {slide_num} ---")
            
            for shape in slide.shapes:
                if hasattr(shape, "text") and shape.text.strip():
                    print(f"文本: {shape.text.strip()}")
                
                # 检查表格
                if shape.has_table:
                    print("发现表格内容")
                    for row in shape.table.rows:
                        row_text = []
                        for cell in row.cells:
                            if cell.text.strip():
                                row_text.append(cell.text.strip())
                        if row_text:
                            print(f"表格行: {' | '.join(row_text)}")
                
                # 检查图表
                if shape.has_chart:
                    print("发现图表内容")
                    try:
                        chart = shape.chart
                        if hasattr(chart, 'chart_title') and chart.chart_title:
                            print(f"图表标题: {chart.chart_title.text_frame.text}")
                    except:
                        print("图表标题提取失败")
        
        # 上传到知识库
        success = upload_ppt_to_knowledge_base(ppt_path)
        
        # 清理临时文件
        os.unlink(ppt_path)
        
        return success
        
    except Exception as e:
        print(f"❌ PPT处理失败: {e}")
        if os.path.exists(ppt_path):
            os.unlink(ppt_path)
        return False

def test_ppt_file_types():
    """测试不同PPT文件类型的支持"""
    print("\n🧪 测试PPT文件类型支持...")
    
    # 测试支持的文件扩展名
    test_extensions = ['ppt', 'pptx']
    
    for ext in test_extensions:
        print(f"\n测试 .{ext} 文件类型...")
        
        # 创建临时文件（模拟不同扩展名）
        if PPTX_AVAILABLE:
            prs = Presentation()
            title_slide_layout = prs.slide_layouts[0]
            slide = prs.slides.add_slide(title_slide_layout)
            title = slide.shapes.title
            title.text = f"测试 {ext.upper()} 文件"
            
            with tempfile.NamedTemporaryFile(suffix=f'.{ext}', delete=False) as temp_file:
                prs.save(temp_file.name)
                temp_file_path = temp_file.name
            
            try:
                # 尝试上传
                with open(temp_file_path, 'rb') as f:
                    files = {'file': (f'test.{ext}', f, 'application/vnd.ms-powerpoint' if ext == 'ppt' else 'application/vnd.openxmlformats-officedocument.presentationml.presentation')}
                    data = {
                        'rag_id': f'test_kb_{ext}',
                        'source': f'test_{ext}_file',
                        'split_strategy': 'hybrid',
                        'update_mode': 'incremental'
                    }
                    
                    response = requests.post(f"{INSERT_URL}/file", files=files, data=data)
                    result = response.json()
                    
                    if result.get("success"):
                        print(f"✅ {ext.upper()} 文件类型支持正常")
                    else:
                        print(f"❌ {ext.upper()} 文件类型处理失败: {result.get('message')}")
                
                # 清理临时文件
                os.unlink(temp_file_path)
                
            except Exception as e:
                print(f"❌ {ext.upper()} 文件测试异常: {e}")
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
        else:
            print(f"⚠️  跳过 {ext.upper()} 测试（python-pptx 未安装）")

def check_service_and_dependencies():
    """检查服务状态和依赖"""
    print("🔍 检查服务状态和依赖...")
    
    # 检查服务状态
    try:
        response = requests.get(f"{INSERT_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 插入服务运行正常")
        else:
            print(f"❌ 插入服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到插入服务: {e}")
        return False
    
    # 检查PPT处理依赖
    if PPTX_AVAILABLE:
        print("✅ python-pptx 库已安装")
    else:
        print("❌ python-pptx 库未安装")
        print("💡 安装命令: pip install python-pptx")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 深能知识库 PPT 文件处理演示")
    print("=" * 50)
    
    # 检查服务和依赖
    if not check_service_and_dependencies():
        print("\n⚠️  请先解决上述问题后再运行演示")
        return
    
    print("\n🚀 开始PPT处理演示...")
    
    # 演示PPT内容提取和上传
    success = demonstrate_ppt_content_extraction()
    
    if success:
        print("\n✅ PPT内容提取和上传演示成功!")
    else:
        print("\n❌ PPT处理演示失败")
    
    # 测试不同文件类型
    test_ppt_file_types()
    
    print("\n" + "=" * 50)
    print("🎉 PPT文件处理演示完成!")
    print("\n💡 PPT处理特点:")
    print("- 自动提取所有幻灯片的文本内容")
    print("- 支持表格内容提取")
    print("- 支持图表标题提取")
    print("- 按幻灯片结构组织内容")
    print("- 支持 .ppt 和 .pptx 两种格式")

if __name__ == "__main__":
    main()
