# PowerPoint 文件处理指南

## 概述

深能知识库系统现已支持 PowerPoint 演示文稿的自动处理和内容提取，可以将 PPT/PPTX 文件中的文本内容智能提取并存储到知识库中。

## 支持的文件格式

- **PowerPoint 97-2003** (`.ppt`)
- **PowerPoint 2007及以上** (`.pptx`)

## 功能特性

### 1. 智能内容提取

系统会自动提取以下内容：

- **幻灯片文本**: 标题、正文、文本框中的所有文字
- **表格内容**: 表格中的文字内容，按行列结构组织
- **图表标题**: 图表的标题文字（如果存在）
- **结构化组织**: 按幻灯片顺序组织内容，保持逻辑结构

### 2. 内容组织方式

提取的内容按以下格式组织：

```
=== 幻灯片 1 ===
[幻灯片1的标题和内容]

=== 幻灯片 2 ===
[幻灯片2的标题和内容]

表格内容:
行1内容 | 列1 | 列2 | 列3
行2内容 | 列1 | 列2 | 列3

图表标题: [图表标题文字]

=== 幻灯片 3 ===
[幻灯片3的标题和内容]
```

## 使用方法

### 1. API 调用

#### 上传 PPT 文件

```bash
curl -X POST "http://localhost:3008/insert/file" \
  -F "file=@presentation.pptx" \
  -F "rag_id=knowledge_base_id" \
  -F "source=presentation_name" \
  -F "split_strategy=hybrid" \
  -F "update_mode=incremental"
```

#### Python 客户端

```python
import requests

def upload_ppt(file_path, rag_id, source=None):
    url = "http://localhost:3008/insert/file"
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {
            'rag_id': rag_id,
            'source': source or file_path,
            'split_strategy': 'hybrid',
            'update_mode': 'incremental'
        }
        
        response = requests.post(url, files=files, data=data)
        return response.json()

# 使用示例
result = upload_ppt("company_report.pptx", "company_kb", "quarterly_report")
print(result)
```

### 2. 响应格式

成功上传后的响应：

```json
{
  "success": true,
  "message": "成功插入 8 个文本块",
  "doc_id": "auto_generated_uuid",
  "chunks_count": 8,
  "details": {
    "split_strategy": "hybrid",
    "update_mode": "incremental",
    "source": "company_presentation.pptx"
  }
}
```

## 文本分块策略

PPT 文件支持所有三种分块策略：

### 1. 固定分块 (fixed)
- 按固定字符数分割内容
- 适合内容密集的技术演示

### 2. 语义分块 (semantic)
- 按语义相似度分割
- 保持相关内容的连贯性
- 适合主题明确的演示

### 3. 混合分块 (hybrid) - 推荐
- 先按语义分块，再按固定大小调整
- 平衡内容连贯性和块大小
- 适合大多数演示文稿

## 最佳实践

### 1. 文件准备

- **清理内容**: 确保PPT中的文本内容清晰、完整
- **结构化**: 使用标准的幻灯片布局，便于内容提取
- **文件大小**: 建议单个PPT文件不超过50MB

### 2. 上传策略

- **增量更新**: 日常更新使用 `incremental` 模式
- **全量更新**: 演示文稿完全修改时使用 `full` 模式
- **来源标识**: 设置清晰的 `source` 标识便于管理

### 3. 分块优化

- **技术文档**: 使用 `semantic` 策略保持技术概念完整
- **培训材料**: 使用 `hybrid` 策略平衡内容和检索效果
- **简报演示**: 使用 `fixed` 策略确保均匀分布

## 技术实现

### 1. 依赖库

系统使用 `python-pptx` 库进行PPT文件处理：

```bash
pip install python-pptx
```

### 2. 处理流程

1. **文件上传**: 接收PPT文件
2. **格式检测**: 自动识别.ppt/.pptx格式
3. **内容提取**: 遍历所有幻灯片提取文本
4. **结构化**: 按幻灯片组织内容
5. **文本分块**: 根据策略进行分块
6. **向量化**: 生成文本向量
7. **存储**: 保存到Milvus数据库

### 3. 错误处理

- **格式不支持**: 提示安装相应依赖
- **文件损坏**: 返回详细错误信息
- **内容为空**: 跳过空白幻灯片
- **提取失败**: 优雅降级处理

## 使用场景

### 1. 企业培训

- **培训课件**: 将培训PPT转换为可搜索的知识库
- **操作手册**: 技术操作步骤的结构化存储
- **政策宣贯**: 公司政策文档的智能检索

### 2. 技术文档

- **产品介绍**: 产品特性和技术规格
- **架构设计**: 系统架构和设计方案
- **技术分享**: 技术会议和分享内容

### 3. 业务报告

- **季度报告**: 业务数据和分析报告
- **项目汇报**: 项目进展和成果展示
- **市场分析**: 市场调研和竞品分析

## 注意事项

### 1. 内容限制

- **图片内容**: 目前不支持图片中的文字识别(OCR)
- **动画效果**: 不处理动画和过渡效果
- **嵌入对象**: 不处理嵌入的视频、音频等多媒体内容

### 2. 格式兼容性

- **版本兼容**: 建议使用较新版本的PowerPoint保存文件
- **字体问题**: 特殊字体可能影响文本提取
- **编码问题**: 确保文件使用标准编码

### 3. 性能考虑

- **文件大小**: 大文件可能需要较长处理时间
- **幻灯片数量**: 幻灯片过多时建议分批处理
- **并发限制**: 避免同时上传大量PPT文件

## 故障排除

### 1. 常见错误

**错误**: `python-pptx 未安装，无法处理PowerPoint文件`
**解决**: 安装依赖库
```bash
pip install python-pptx
```

**错误**: `不支持的文件类型: ppt`
**解决**: 检查文件扩展名，确保为.ppt或.pptx

**错误**: `Failed to extract text from PPT file`
**解决**: 检查PPT文件是否损坏，尝试用PowerPoint重新保存

### 2. 调试方法

1. **检查文件**: 确认PPT文件可以正常打开
2. **查看日志**: 检查服务器日志获取详细错误信息
3. **测试上传**: 使用简单的PPT文件进行测试
4. **验证依赖**: 确认python-pptx库正确安装

## 示例代码

完整的PPT处理示例请参考：
- `examples/ppt_processing_example.py` - 完整演示代码
- `tests/test_insert_api.py` - 测试用例

## 更新日志

- **v1.0**: 基础PPT文件支持
- **v1.1**: 添加表格内容提取
- **v1.2**: 支持图表标题提取
- **v1.3**: 优化内容结构化组织

## 技术支持

如遇到PPT处理相关问题，请：

1. 检查依赖库安装
2. 查看错误日志
3. 验证文件格式
4. 联系技术支持团队
