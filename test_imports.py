"""
测试导入是否正常工作
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """测试各个模块的导入"""
    print("🔍 测试模块导入...")
    
    try:
        print("1. 测试 milvus 模块导入...")
        from operation.milvus import MilvusVanna, milvus_client
        print("   ✅ milvus 模块导入成功")
    except Exception as e:
        print(f"   ❌ milvus 模块导入失败: {e}")
        return False
    
    try:
        print("2. 测试 text_splitter 模块导入...")
        from operation.text_splitter import TextSplitter, SplitConfig
        print("   ✅ text_splitter 模块导入成功")
    except Exception as e:
        print(f"   ❌ text_splitter 模块导入失败: {e}")
        return False
    
    try:
        print("3. 测试 delete 模块导入...")
        from operation.delete import MilvusDelete
        print("   ✅ delete 模块导入成功")
    except Exception as e:
        print(f"   ❌ delete 模块导入失败: {e}")
        return False
    
    try:
        print("4. 测试 insert 模块导入...")
        from operation.insert import MilvusInsert
        print("   ✅ insert 模块导入成功")
    except Exception as e:
        print(f"   ❌ insert 模块导入失败: {e}")
        return False
    
    try:
        print("5. 测试 query 模块导入...")
        from operation.query import router as query_router
        print("   ✅ query 模块导入成功")
    except Exception as e:
        print(f"   ❌ query 模块导入失败: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        print("1. 测试 TextSplitter 初始化...")
        from operation.text_splitter import TextSplitter
        splitter = TextSplitter()
        print("   ✅ TextSplitter 初始化成功")
    except Exception as e:
        print(f"   ❌ TextSplitter 初始化失败: {e}")
        return False
    
    try:
        print("2. 测试文本分块功能...")
        test_text = "这是一个测试文本。它包含多个句子。我们用它来测试分块功能。"
        chunks = splitter.split(test_text, strategy="fixed")
        print(f"   ✅ 文本分块成功，生成 {len(chunks)} 个块")
    except Exception as e:
        print(f"   ❌ 文本分块失败: {e}")
        return False
    
    try:
        print("3. 测试 MilvusVanna 初始化...")
        from operation.milvus import MilvusVanna
        # 注意：这里可能会因为Milvus连接失败而报错，但导入应该成功
        print("   ✅ MilvusVanna 类导入成功")
    except Exception as e:
        print(f"   ❌ MilvusVanna 初始化失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 开始测试深能知识库系统模块导入")
    print("=" * 50)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        print("\n✅ 所有模块导入成功！")
        
        # 测试基本功能
        func_success = test_basic_functionality()
        
        if func_success:
            print("\n🎉 基本功能测试通过！")
            print("\n💡 现在可以运行以下命令启动服务：")
            print("   python app.py")
        else:
            print("\n⚠️  基本功能测试失败，请检查配置")
    else:
        print("\n❌ 模块导入失败，请检查以下问题：")
        print("1. 确保所有依赖已安装")
        print("2. 检查文件路径是否正确")
        print("3. 确保 config.py 配置正确")
        print("4. 检查 Milvus 服务是否运行")

if __name__ == "__main__":
    main()
