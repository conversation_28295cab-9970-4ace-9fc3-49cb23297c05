"""
Markdown文件处理示例
演示如何处理Markdown文件并插入到知识库
"""
import requests
import json
import tempfile
import os

# 配置
BASE_URL = "http://localhost:3008"
INSERT_URL = f"{BASE_URL}/insert"

def create_sample_markdown():
    """创建一个示例Markdown文件"""
    print("📝 创建示例Markdown文件...")
    
    markdown_content = """# 深能集团清洁能源技术白皮书

## 执行摘要

深能集团作为中国领先的清洁能源企业，致力于推动**可持续能源发展**和*碳中和目标*的实现。本白皮书详细介绍了我们在清洁能源领域的技术创新、项目实践和未来规划。

## 1. 公司概况

### 1.1 企业简介

深能集团成立于2008年，总部位于深圳，是一家专注于清洁能源技术研发、设备制造和项目运营的综合性企业集团。

**核心业务领域：**
- 风力发电技术与设备
- 太阳能光伏系统
- 储能技术解决方案
- 智能电网与能源管理

### 1.2 发展历程

| 年份 | 重要里程碑 | 装机容量 |
|------|-----------|----------|
| 2008 | 公司成立，首个风电项目启动 | 50MW |
| 2012 | 进入光伏发电领域 | 500MW |
| 2016 | 海上风电技术突破 | 1500MW |
| 2020 | 储能业务全面布局 | 3000MW |
| 2024 | 智慧能源平台上线 | 5000MW |

## 2. 技术创新

### 2.1 风力发电技术

#### 2.1.1 海上风电技术

深能集团在海上风电领域取得了重大突破：

> "我们自主研发的10MW海上风机，在恶劣海洋环境下仍能保持95%以上的可用率，这是行业领先水平。" 
> —— 技术总监 张工

**技术特点：**
1. **大容量风机设计**
   - 单机容量达到10MW
   - 叶轮直径180米
   - 年发电量4000万kWh

2. **抗台风技术**
   - 可抗17级台风
   - 智能变桨控制系统
   - 自动安全停机保护

3. **海洋环境适应性**
   - 防腐蚀材料应用
   - 海水冷却系统
   - 远程监控维护

#### 2.1.2 陆上风电优化

```python
# 风电场功率预测算法示例
def wind_power_prediction(wind_speed, air_density, rotor_area):
    """
    计算风电机组理论功率输出
    """
    power = 0.5 * air_density * rotor_area * (wind_speed ** 3)
    return power * 0.4  # 考虑实际效率

# 示例计算
wind_speed = 12  # m/s
air_density = 1.225  # kg/m³
rotor_area = 25446  # m² (180m直径)

predicted_power = wind_power_prediction(wind_speed, air_density, rotor_area)
print(f"预测功率输出: {predicted_power/1000:.2f} MW")
```

### 2.2 太阳能光伏技术

#### 2.2.1 高效组件技术

- **PERC技术应用**：电池效率达到22.5%
- **双面发电技术**：背面发电增益15-25%
- **半片技术**：降低功率损失，提高可靠性

#### 2.2.2 智能跟踪系统

```bash
# 太阳能跟踪系统控制命令
./solar_tracker --mode=dual_axis --precision=0.1 --weather_compensation=true
```

### 2.3 储能技术

#### 2.3.1 锂电池储能系统

**技术参数：**
- 系统容量：100MWh
- 充放电效率：≥95%
- 循环寿命：≥8000次
- 响应时间：<100ms

#### 2.3.2 氢能储能技术

深能集团正在开发下一代氢能储能技术：

- [x] 电解水制氢技术
- [x] 氢气储存系统
- [ ] 燃料电池发电
- [ ] 氢能交通应用

## 3. 项目案例

### 3.1 江苏如东海上风电场

**项目概况：**
- **装机容量**：300MW
- **风机数量**：30台 × 10MW
- **年发电量**：8亿kWh
- **投资规模**：45亿元

**技术亮点：**
1. 采用自主研发的10MW海上风机
2. 配备先进的海缆传输系统
3. 建设海上升压站
4. 实现无人值守运行

### 3.2 新疆哈密光伏电站

**项目特色：**
- 装机容量200MW
- 采用双面发电组件
- 配置50MWh储能系统
- 年减排CO₂约20万吨

### 3.3 深圳智慧园区项目

这是一个综合性的智慧能源示范项目：

```mermaid
graph TD
    A[太阳能发电] --> D[智能配电系统]
    B[风力发电] --> D
    C[储能系统] --> D
    D --> E[园区用电负荷]
    D --> F[电网并网]
    G[能源管理系统] --> D
```

## 4. 技术路线图

### 4.1 短期目标（2025-2027）

- [ ] 15MW海上风机技术突破
- [ ] 钙钛矿太阳能电池产业化
- [ ] 液流电池储能技术应用
- [ ] AI驱动的智能运维平台

### 4.2 中期目标（2028-2030）

- [ ] 漂浮式海上风电技术
- [ ] 氢能全产业链布局
- [ ] 碳捕集与利用技术
- [ ] 虚拟电厂运营平台

### 4.3 长期愿景（2030+）

> 成为全球领先的清洁能源技术创新者和综合服务商，为实现碳中和目标贡献力量。

## 5. 可持续发展

### 5.1 环境效益

**累计环境贡献：**
- 年发电量：150亿kWh
- 年减排CO₂：800万吨
- 相当于植树造林：20万亩

### 5.2 社会责任

深能集团积极履行社会责任：

1. **技术扶贫**：为偏远地区提供清洁能源解决方案
2. **人才培养**：与高校合作培养清洁能源专业人才
3. **产业带动**：带动上下游产业链发展，创造就业机会

## 6. 联系信息

**深能集团总部**
- 地址：深圳市南山区科技园南区
- 电话：+86-755-8888-0000
- 邮箱：<EMAIL>
- 网站：[www.shenneng.com](https://www.shenneng.com)

**技术合作**
- 研发中心：<EMAIL>
- 项目合作：<EMAIL>

---

*本白皮书版权归深能集团所有，最后更新时间：2025年6月*

![深能集团](company-logo.png "深能集团 - 清洁能源领导者")

**免责声明**：本文档中的技术参数和项目数据仅供参考，具体以实际项目为准。
"""
    
    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8')
    temp_file.write(markdown_content)
    temp_file.close()
    
    print(f"✅ 示例Markdown文件已创建: {temp_file.name}")
    return temp_file.name

def upload_markdown_to_knowledge_base(md_file_path, rag_id="shenneng_tech_whitepaper"):
    """上传Markdown文件到知识库"""
    print(f"\n📤 上传Markdown文件到知识库...")
    
    try:
        with open(md_file_path, 'rb') as f:
            files = {'file': ('shenneng_tech_whitepaper.md', f, 'text/markdown')}
            data = {
                'rag_id': rag_id,
                'source': 'shenneng_technical_whitepaper_2025',
                'split_strategy': 'semantic',  # 使用语义分块保持技术内容连贯性
                'update_mode': 'incremental'
            }
            
            response = requests.post(f"{INSERT_URL}/file", files=files, data=data)
            result = response.json()
            
            print(f"上传状态: {response.status_code}")
            print(f"上传结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            if result.get("success"):
                print(f"✅ Markdown文件上传成功!")
                print(f"📄 文档ID: {result['doc_id']}")
                print(f"📊 生成文本块数量: {result['chunks_count']}")
                return True
            else:
                print(f"❌ Markdown文件上传失败: {result.get('message')}")
                return False
                
    except Exception as e:
        print(f"❌ Markdown文件上传异常: {e}")
        return False

def demonstrate_markdown_processing():
    """演示Markdown内容处理功能"""
    print("\n🔍 演示Markdown内容处理功能...")
    
    # 创建示例Markdown
    md_path = create_sample_markdown()
    if not md_path:
        return False
    
    try:
        # 显示原始内容预览
        print("\n📖 Markdown内容预览（前500字符）:")
        with open(md_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content[:500] + "..." if len(content) > 500 else content)
        
        # 上传到知识库
        success = upload_markdown_to_knowledge_base(md_path)
        
        # 清理临时文件
        os.unlink(md_path)
        
        return success
        
    except Exception as e:
        print(f"❌ Markdown处理失败: {e}")
        if os.path.exists(md_path):
            os.unlink(md_path)
        return False

def test_markdown_features():
    """测试Markdown特性处理"""
    print("\n🧪 测试Markdown特性处理...")
    
    # 测试各种Markdown语法
    test_markdown = """# 测试标题

## 二级标题

这是**粗体文本**和*斜体文本*。

### 列表测试

1. 有序列表项1
2. 有序列表项2
   - 嵌套无序列表
   - 另一个嵌套项

- 无序列表项1
- 无序列表项2

### 代码测试

这是`行内代码`示例。

```python
def hello_world():
    print("Hello, World!")
```

### 表格测试

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

### 链接和图片

这是一个[链接示例](https://example.com)。

![图片描述](image.png)

### 引用

> 这是一个引用块
> 可以包含多行内容

### 任务列表

- [x] 已完成任务
- [ ] 未完成任务
"""
    
    try:
        # 创建测试文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
            f.write(test_markdown)
            temp_file_path = f.name
        
        # 上传测试
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('markdown_features_test.md', f, 'text/markdown')}
            data = {
                'rag_id': 'test_kb_md_features',
                'source': 'markdown_features_test',
                'split_strategy': 'fixed',
                'update_mode': 'incremental'
            }
            
            response = requests.post(f"{INSERT_URL}/file", files=files, data=data)
            result = response.json()
            
            print(f"Markdown特性测试状态: {response.status_code}")
            if result.get("success"):
                print(f"✅ Markdown特性处理成功，生成 {result['chunks_count']} 个文本块")
            else:
                print(f"❌ Markdown特性处理失败: {result.get('message')}")
        
        # 清理临时文件
        os.unlink(temp_file_path)
        
        return response.status_code == 200 and result.get("success", False)
        
    except Exception as e:
        print(f"❌ Markdown特性测试异常: {e}")
        if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        return False

def check_service_status():
    """检查服务状态"""
    print("🔍 检查服务状态...")
    
    try:
        response = requests.get(f"{INSERT_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 插入服务运行正常")
            return True
        else:
            print(f"❌ 插入服务异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到插入服务: {e}")
        return False

def main():
    """主函数"""
    print("📝 深能知识库 Markdown 文件处理演示")
    print("=" * 50)
    
    # 检查服务状态
    if not check_service_status():
        print("\n⚠️  请先启动服务后再运行演示")
        return
    
    print("\n🚀 开始Markdown处理演示...")
    
    # 演示Markdown内容处理和上传
    success1 = demonstrate_markdown_processing()
    
    # 测试Markdown特性
    success2 = test_markdown_features()
    
    print("\n" + "=" * 50)
    print("🎉 Markdown文件处理演示完成!")
    
    if success1 and success2:
        print("✅ 所有测试都成功完成!")
    else:
        print("⚠️  部分测试失败，请检查日志")
    
    print("\n💡 Markdown处理特点:")
    print("- 自动处理标题、列表、表格等结构")
    print("- 保留文本内容，移除格式标记")
    print("- 支持代码块和引用内容提取")
    print("- 智能处理链接和图片描述")
    print("- 适合技术文档和说明文档")
    print("- 推荐使用语义分块策略保持内容连贯性")

if __name__ == "__main__":
    main()
